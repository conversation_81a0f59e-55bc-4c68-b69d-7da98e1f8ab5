import dayjs, { Dayjs } from "dayjs";

export const formatWeekStartDate = (week: number) => {
  return dayjs()?.week(week)?.startOf("week")?.format("MM/DD");
};

export const formatWeekEndofDate = (week: number) => {
  return dayjs()?.week(week)?.endOf("week")?.format("MM/DD");
};

export const isNullObject = (obj: any) => {
  return (
    Object.prototype.toString.call(obj) === "[object Object]" &&
    Object.keys(obj).length === 0
  );
};

export const filterOption = (
  input: string,
  option?: { label: string; value: string }
) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

export const isNumber = (value: any) => {
  return typeof value === "number" && !isNaN(value);
};

/**
 * 检查值是否为 dayjs 对象
 * @param value 要检查的值
 * @returns 是否为 dayjs 对象
 */
export const isDayjsObject = (value: any): value is Dayjs => {
  return (
    value &&
    typeof value === "object" &&
    typeof value.format === "function" &&
    dayjs.isDayjs(value)
  );
};

/**
 * 检查值是否为 moment 对象
 * @param value 要检查的值
 * @returns 是否为 moment 对象
 */
export const isMomentObject = (value: any): boolean => {
  return (
    value &&
    typeof value === "object" &&
    typeof value.format === "function" &&
    typeof value.isValid === "function" &&
    // 检查是否有 moment 特有的属性
    (value._isAMomentObject === true ||
      (value.constructor && value.constructor.name === "Moment"))
  );
};

/**
 * 检查值是否为日期对象（dayjs 或 moment）
 * @param value 要检查的值
 * @returns 是否为日期对象
 */
export const isDateObject = (value: any): boolean => {
  return isDayjsObject(value) || isMomentObject(value);
};

/**
 * 格式化日期值，如果是 dayjs 或 moment 对象则使用指定格式，否则返回原值
 * @param value 要格式化的值
 * @param format 日期格式，默认为 "YYYY-MM-DD HH:mm:ss"
 * @returns 格式化后的值
 */
export const formatDateValue = (value: any, format: string) => {
  if (!format) {
    return value;
  }
  if (isDateObject(value)) {
    return value.format(format);
  }
  return value;
};

export const deepClone = (source: any) => {
  if (typeof source !== "object") {
    return source;
  }
  const temp = Array.isArray(source) ? [] : {};
  for (const [key, value] of Object.entries(source)) {
    temp[key] = deepClone(value);
  }

  return temp;
};
