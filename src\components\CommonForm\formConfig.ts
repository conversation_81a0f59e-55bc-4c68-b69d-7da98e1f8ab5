import { ReactNode } from "react";
import { Rule } from "antd/lib/form";
import { Dayjs } from "dayjs";

// 通用类型
export type AnyFunction = (...args: any[]) => any;

// 禁用时间类型
export type DisabledTime = (now: Dayjs) => {
  disabledHours?: () => number[];
  disabledMinutes?: (selectedHour: number) => number[];
  disabledSeconds?: (selectedHour: number, selectedMinute: number) => number[];
  disabledMilliseconds?: (
    selectedHour: number,
    selectedMinute: number,
    selectedSecond: number
  ) => number[];
};

// 表单字段类型
export type FieldType =
  | "input"
  | "select"
  | "rangeTime"
  | "cascader"
  | "textarea"
  | "radioGroup"
  | "checkboxGroup"
  | "datePicker"
  | "timePicker"
  | "customize"
  | "dateTime"
  | "switch"
  | "upload"
  | "inputNumber"
  | "ReactNode"
  | "text";

// 文件上传单位
export type FileUnit = "MB" | "GB" | "KB";

// 级联选择器显示策略
export type ShowCheckedStrategy = "SHOW_PARENT" | "SHOW_CHILD";

// 日期选择器类型
export type DatePickerType = "date" | "week" | "month" | "quarter" | "year";

// 验证规则类型
export interface ValidatorRule {
  required: boolean;
  message: string;
  validator?: (rule: any, value: any) => Promise<any>;
}

// 表单项基础接口
export interface BaseFieldItem {
  fieldName?: string;
  label?: ReactNode;
  type?: FieldType;
  placeholder?: string;
  hidden?: boolean;
  required?: boolean;
  validatorRules?: ValidatorRule[] | any[] | null;
  help?: ReactNode;
  extra?: ReactNode;
  tooltip?: string | ReactNode;
  disabled?: boolean;
  isChild?: boolean;
  clstag?: string;
  onBlur?: AnyFunction;
}

// 布局相关属性
export interface LayoutProps {
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  childrenList?: string[];
  width?: string;
  marginLeft?: number;
  marginRight?: number;
  xxl?: number;
  xl?: number;
  lg?: number;
  md?: number;
}

// 输入框特有属性
export interface InputFieldProps {
  maxLength?: number;
  autoSize?: boolean | object;
  showCount?: boolean;
  formatter?: (value: any) => string;
  parser?: any;
  precision?: number;
  step?: number;
  max?: number;
  min?: number;
  controls?: boolean;
  addonBefore?: ReactNode;
  addonAfter?: ReactNode;
}

// 选择器特有属性
export interface SelectFieldProps {
  options?: any[];
  showSearch?: boolean;
  allowClear?: boolean;
  maxTagCount?: number;
  multiple?: boolean;
  showSelectAll?: boolean;
  mapRelation?: object;
  changeOnSelect?: boolean;
  labelInValue?: boolean;
  showCheckedStrategy?: ShowCheckedStrategy;
  checkable?: boolean;
}

// 时间选择器特有属性
export interface TimeFieldProps {
  disabledTime?: DisabledTime;
  disabledDate?: AnyFunction;
  showNow?: boolean;
  needConfirm?: boolean;
  format?: string;
  hideDisabledOptions?: boolean;
  showTime?: Object | boolean;
  picker?: DatePickerType;
}

// 上传组件特有属性
export interface UploadFieldProps {
  fileListType?: "picture" | "file";
  accept?: string;
  bucketName?: string;
  LOPDN?: string;
  getPreSignatureUrl?: string;
  maxFileSize?: number;
  unit?: FileUnit;
  maxFileCount?: number;
  uploadedFileList?: any[];
}

// 开关组件特有属性
export interface SwitchFieldProps {
  defaultChecked?: boolean;
}

// 完整的字段项接口
export interface FieldItem
  extends BaseFieldItem,
    LayoutProps,
    Partial<InputFieldProps>,
    Partial<SelectFieldProps>,
    Partial<TimeFieldProps>,
    Partial<UploadFieldProps>,
    Partial<SwitchFieldProps> {
  renderFunc?: (data: any, value?: any) => ReactNode;
  departmentParams?: Object;
}

// 联动规则类型
/**
 * 当前元素要发生什么变化
 * fetchData->获取数据(比如请求下拉框内容)
 * clear->清空值
 * refresh->更新为初始值
 * visible->表单项是否展示
 * valueDisable->数据不可用
 * fieldItemDisable->表单项不可用
 */
export type LinkRuleType =
  | "fetchData"
  | "clear"
  | "refresh"
  | "visible"
  | "valueDisable"
  | "fieldItemDisable";

// 联动规则接口
export interface LinkRule {
  linkFieldName: string;
  rule: LinkRuleType;
  dependenceData?: any[]; // 依赖元素的值变为哪个时当前元素发生变化
  disabledValue?: any[]; // rule为valueDisable时，哪个值不可用
  fetchFunc?: Function; // rule为fetchData时，请求数据的函数
  refreshFunc?: Function;
}

// 表单配置接口
export interface FormConfig {
  fields: FieldItem[];
  linkRules?: {
    [fieldName: string]: LinkRule[];
  };
}

// 表单属性接口
export interface FormProps {
  name?: string;
  theme?: "light" | "dark";
  formConfig: FormConfig;
  layout?: "horizontal" | "vertical" | "inline";
  defaultValue?: Record<string, any>;
  initLink?: boolean;
  formType?: "search" | "edit";
  colon?: boolean;
  className?: string;
  labelAlign?: "left" | "right" | undefined;
  searchBtnClstag?: string;
  resetBtnClstag?: string;
  onValueChange?: (values: any, changedField: string) => void;
  getFormInstance?: (form: any) => void;
  onResetClick?: () => void;
  onSearchClick?: (values: any) => void;
  onFieldFocus?: (fieldName: string | undefined, values: any) => void;
  getUploadStatus?: (
    status: boolean,
    uploadStatus: "uploading" | "error" | "success" | "delete",
    fieldName?: string
  ) => void;
}
